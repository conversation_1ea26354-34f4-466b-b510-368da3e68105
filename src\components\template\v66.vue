<template>
  <div class="page-container">
    <AlertBar theme="orange" />

    <Header @click="navigateToIndex" />

    <div class="amount">
      <div class="title">您想最高借多少(元)</div>
      <div class="input-container">
        <input
          placeholder="请输入金额"
          v-model="form.demandAmount"
          @blur="demandAmountBlur"
          @focus="demandAmountFocus"
        />
        <div class="all-btn" @click="clickMaxAmount">全部借出</div>
      </div>
      <div class="slider-container">
        <van-slider
          :step="100 / 3"
          @change="demandAmountSliderChange"
          v-model="form.demandAmountSlider"
          inactive-color="#F7F7F7"
        />
        <div class="slider-tick">
          <span>5万</span>
          <span>10万</span>
          <span>15万</span>
          <span>20万</span>
        </div>
      </div>
      <div class="annual-interest-rate">
        <div class="tag">限时优惠</div>
        <span>
          参考年化利率
          <span class="highlight">12%</span>
          ,1000元1天仅需
          <span class="highlight">0.3</span>
          元
        </span>
      </div>
    </div>

    <div class="borrowing-options">
      <div class="option term" @click="showMonthPicker = true">
        <div class="label">最长借多久</div>
        <div class="value">
          <div>{{ monthRange[form.monthIndex].value }}个月</div>
          <div class="recommend" v-if="monthRange[form.monthIndex].value === 12">推荐</div>
          <van-icon name="arrow" color="#A2A3A5" size="16px" style="margin-left: 15px" />
        </div>
      </div>
      <div class="option-line"></div>
      <div class="option repayment-method">
        <div class="label">如何还</div>
        <div class="value">
          <div class="repayment-amount">
            每月约应还
            <span class="amount-number">￥{{ monthlyPay }}</span>
          </div>
          <div class="repayment-reminder">实际贷款利息及放款金额以最终审批为准</div>
        </div>
      </div>
      <div class="option-line"></div>
      <div class="option coupon">
        <div class="label">优惠券</div>
        <div class="value">
          <div class="coupon-container">
            <div class="coupon-text">
              新用户借款享
              <span class="days">30天</span>
              免息
            </div>
            <div class="coupon-tips"> 若提前还清或逾期，本券将失效 </div>
          </div>
          <van-icon name="arrow" color="#A2A3A5" size="16px" />
        </div>
      </div>
    </div>

    <div class="phone-container">
      <input
        type="tel"
        maxlength="11"
        class="phone-input"
        placeholder="输入手机号获取额度(已加密)"
        @blur="phoneBlur"
        v-model="form.phone"
      />
      <div class="get-my-quota" @click="clickGetMyQuota">领取我的额度</div>
      <div class="agreement" @click="isAgree = !isAgree">
        <img
          v-if="isAgree"
          class="agree-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/674fd4e2b9c24182bd15929f85634629.png"
        />
        <img
          v-else
          class="agree-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/f83966b496f348bba3ba7145ac281ab2.png"
        />

        <div class="agreement-text">
          我已阅读并同意
          <span
            class="name"
            v-for="item in agreementList"
            :key="item.protocolId"
            @click="clickAgreement(item)"
          >
            《{{ item.name }}》
          </span>
        </div>
      </div>
    </div>

    <Declaration />

    <!-- 月份选择器弹窗 -->
    <van-popup v-model:show="showMonthPicker" position="bottom" round>
      <van-picker
        title="选择借款期限"
        :columns="monthRange"
        :default-index="form.monthIndex"
        @confirm="monthPickerConfirm"
        @cancel="showMonthPicker = false"
      />
    </van-popup>

    <!-- 协议弹窗 -->
    <van-popup
      v-model:show="showAgreementPopup"
      position="bottom"
      round
      style="background-color: #fff"
    >
      <div class="agreement-popup">
        <div class="agreement-container">
          <div v-for="agreement in agreementList" :key="agreement.protocolId">
            <div class="agreement-content">
              <div v-html="agreement.content"></div>
            </div>
          </div>
        </div>
        <div class="agreement-agree-container">
          <div class="agreement-agree-btn" @click="clickAgreeAndContinue">同意并继续</div>
        </div>
      </div>
    </van-popup>

    <!-- 验证码弹窗 -->
    <van-popup
      v-model:show="showCodePopup"
      :close-on-click-overlay="false"
      position="center"
      round
      style="background-color: #fff"
    >
      <div class="code-popup">
        <div class="code-popup-title">
          恭喜，已为您
          <span class="highlight">锁定借款名额</span>
        </div>
        <div class="code-input-container">
          <input
            maxlength="6"
            type="text"
            v-model="form.code"
            placeholder="输入手机验证码"
          />
          <div class="gap-line"></div>
          <div class="get-code-btn">
            <template v-if="codeTimer">{{ codeCountdown }}s</template>
            <template v-else>
              <span @click="clickGetCode">获取</span>
            </template>
          </div>
        </div>
        <div class="get-amount-btn" @click="clickGetAmount">获取额度</div>

        <img
          src="https://cdn.oss-unos.hmctec.cn/common/path/308fb51bf41943c2aec94bc29ef954ae.png"
          class="close-btn"
          @click="showCodePopup = false"
        />
      </div>
    </van-popup>

    <RemindPopup ref="remindPopup" />
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { showToast, VanSlider, VanIcon, VanPopup, VanPicker } from 'vant'
import { validateMobile } from '@/utils/validators'
import AlertBar from '@/components/alert/AlertBar.vue'
import LockNativeBack from '@/utils/lock-native-back'
import { reportUV } from '@/apis/common'
import { saveLoan, sendSmsCode } from '@/apis/common-2'
import { encryptByDES } from '@/utils/encrypt'

import throttle from '@/utils/throttle'
import Header from '@/components/header/header-hyh.vue'
import { formatAmount, parseAmount } from '@/utils/amount'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import Declaration from '@/components/footer/declaration-component/declaration-zxbc.vue'
import RemindPopup from '@/components/hyh/index/RemindPopup.vue'
import { getAgreements } from '@/utils/agreement'
import { useUserStore } from '@/store/modules/user'



// Props
const props = defineProps({
  channelId: {
    type: [String, Number],
    default: ''
  }
})

// 响应式数据
const form = reactive({
  demandAmount: '50,000',
  demandAmountSlider: 0,
  monthIndex: 2,
  phone: '',
  channelId: '',
  code: ''
})

const presetValues = [0, 33.33, 66.66, 100] // 对应 5万、10万、15万、20万
const presetAmounts = [50000, 100000, 150000, 200000]
const monthRange = [
  {
    text: '3个月',
    value: 3
  },
  {
    text: '6个月',
    value: 6
  },
  {
    text: '12个月',
    value: 12
  },
  {
    text: '36个月',
    value: 36
  }
]

const monthlyPay = ref('')
const isAgree = ref(false)
const codeTimer = ref(null)
const codeCountdown = ref(0)
const lockBack = ref(null)
const agreementList = ref([])

// 弹窗显示状态
const showMonthPicker = ref(false)
const showAgreementPopup = ref(false)
const showCodePopup = ref(false)

// 计算属性

// Refs
const remindPopup = ref(null)

// 生命周期钩子
onBeforeUnmount(() => {
  if (codeTimer.value) {
    clearInterval(codeTimer.value)
    codeTimer.value = null
  }

  if (lockBack.value) {
    lockBack.value.unLock()
    lockBack.value = null
  }
})

onMounted(() => {
  if (props.channelId) {
    form.channelId = props.channelId
    reportUV({ channelId: props.channelId })
  }
  fetchAgreement()
  updateAmountUI()
  // 初始化 LockNativeBack 实例
  lockBack.value = new LockNativeBack({
    onPopState: () => {
      // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
      remindPopup.value?.open()
    }
  })
  lockBack.value.lock()
})

// 方法
const fetchAgreement = async () => {
  // 注册页协议
  agreementList.value =
    (await getAgreements('hyh_info_stream_registration_page_agreement')) || []
}

const clickGetMyQuota = () => {
  throttle(getMyQuotaHandler)
}

const getMyQuotaHandler = () => {
  if (!form.phone) {
    showToast('请输入手机号')
    return
  }

  if (!validateMobile(form.phone)) {
    showToast('请输入正确的手机号')
    return
  }

  if (!isAgree.value) {
    showAgreementPopup.value = true
    return
  }

  showCodePopup.value = true
  // 打开验证码弹窗时，自动获取验证码
  if (!codeTimer.value) {
    clickGetCode()
  }
}

const clickAgreement = () => {
  showAgreementPopup.value = true
}

const phoneBlur = () => {
  if (form.phone && !validateMobile(form.phone)) {
    showToast('请输入正确的手机号')
  }
}

const clickGetCode = () => {
  getCodeHandler()
}

const getCodeHandler = async () => {
  const res = await sendSmsCode({
    phone: form.phone,
    channelId: form.channelId
  })

  if (res.code != 200) {
    showToast(res.msg || '发送失败')
    return
  }

  showToast('发送成功')

  codeCountdown.value = 60
  if (codeTimer.value) {
    clearInterval(codeTimer.value)
    codeTimer.value = null
  }
  codeTimer.value = setInterval(() => {
    codeCountdown.value--
    if (codeCountdown.value <= 0) {
      clearInterval(codeTimer.value)
      codeTimer.value = null
    }
  }, 1000)
}

const demandAmountSliderChange = (value) => {
  const sliderValue = value

  // 找出最近的预设值
  const closestPreset = presetValues.reduce((prev, curr) =>
    Math.abs(curr - sliderValue) < Math.abs(prev - sliderValue) ? curr : prev
  )

  // 更新金额
  const amountIndex = presetValues.indexOf(closestPreset)
  const amount = presetAmounts[amountIndex]
  form.demandAmount = formatAmount(amount)
  updateAmountUI()
}

const clickMaxAmount = () => {
  form.demandAmount = '200,000'
  updateAmountUI()
}

const computedMonthPay = () => {
  let price = Number(parseAmount(form.demandAmount))
  let mLatte = (price * 12) / 100 / 12
  const month = monthRange[form.monthIndex].value

  monthlyPay.value = ((price + mLatte * month) / month).toFixed(2)
}

const updateAmountUI = () => {
  setSliderValue(parseAmount(form.demandAmount))
  computedMonthPay()
}

const demandAmountBlur = () => {
  const amount = parseInt(form.demandAmount)
  const isNotNumber = isNaN(amount)
  // 只允许输入 1万的整数倍
  const isNotValid = amount % 10000 !== 0

  if (isNotNumber || isNotValid) {
    showToast('请输入万的整数倍')
    form.demandAmount = formatAmount(50000)
    updateAmountUI()
    return
  }

  // 允许的范围是 50000～200000
  if (amount < 50000) {
    showToast('最低借款金额为5万')
    form.demandAmount = formatAmount(50000)
    updateAmountUI()
    return
  }

  if (amount > 200000) {
    showToast('最高借款金额为20万')
    form.demandAmount = formatAmount(200000)
    updateAmountUI()
    return
  }

  form.demandAmount = formatAmount(amount)
  updateAmountUI()
}

const demandAmountFocus = () => {
  form.demandAmount = parseAmount(form.demandAmount)
}

// 根据传入的金额，设置滑块的值
const setSliderValue = (amount) => {
  if (amount <= presetAmounts[0]) {
    form.demandAmountSlider = presetValues[0]
  } else if (amount >= presetAmounts[presetAmounts.length - 1]) {
    form.demandAmountSlider = presetValues[presetValues.length - 1]
  } else {
    for (let i = 1; i < presetAmounts.length; i++) {
      if (amount <= presetAmounts[i]) {
        const lowerAmount = presetAmounts[i - 1]
        const upperAmount = presetAmounts[i]
        const lowerValue = presetValues[i - 1]
        const upperValue = presetValues[i]

        const ratio = (amount - lowerAmount) / (upperAmount - lowerAmount)
        form.demandAmountSlider = lowerValue + ratio * (upperValue - lowerValue)
        break
      }
    }
  }
}

const monthPickerConfirm = ({ selectedOptions }) => {
  const selectedIndex = monthRange.findIndex(item => item.text === selectedOptions[0].text)
  form.monthIndex = selectedIndex
  showMonthPicker.value = false
  updateAmountUI()
}

const clickAgreeAndContinue = () => {
  showAgreementPopup.value = false
  isAgree.value = true
  clickGetMyQuota()
}

const clickGetAmount = () => {
  throttle(() => {
    if (!form.code) {
      showToast('请输入验证码')
      return
    }

    login()
  })
}



const getLoginParams = async () => {
  const params = {}

  params.phoneBlack = getBlackPhone()
  // params.h5UaUuid = await getVisitorId();
  params.deviceType = uni.getSystemInfoSync().platform
  params.demandAmount = parseAmount(form.demandAmount)
  params.phone = form.phone
  params.code = form.code
  params.channelId = form.channelId

  return params
}

const login = async () => {
  const params = await getLoginParams()

  const res = await saveLoan(params)

  if (res.code != 200) {
    showToast(res.msg || '操作失败')
    return
  }

  // 登录成功后，记录手机号
  setBlackPhone(params.phone)
  const userStore = useUserStore()
  userStore.setPhone(params.phone)
  userStore.setConsumerId(res.data)

  const urlParam = {
    consumerId: res.data,
    phone: form.phone,
    demandAmount: parseAmount(form.demandAmount),
    channelId: form.channelId,
    monthIndex: form.monthIndex
  }
  const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
  uni.navigateTo({
    url: `/extreme/v66/auth/index?param=${urlParamString}`
  })
}

const navigateToIndex = () => {
  // 导航到首页的逻辑
}
</script>
<style lang="scss" scoped>
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}

.page-container {
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  background: #f6f6f8;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 438rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/3d34ff5b56ae4f299c8ef6f00cd59fc2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: scale(3);
  }
}

.alert {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 10rpx 30rpx;
  background-color: #fff7eb;

  .alert-icon {
    width: 51rpx;
    height: 29rpx;
  }

  .alert-notice {
    padding: 0;
    margin: 0;
  }
}

.platform {
  position: relative;
  padding: 30rpx;
  display: flex;
  align-items: flex-end;
  gap: 20rpx;

  .platform-name {
    color: #16283c;
    font-family: 'Alimama ShuHeiTi', serif;
    font-weight: bold;
    font-size: 40rpx;
  }

  .platform-feature {
    font-weight: 400;
    font-size: 24rpx;
    color: #4a5159;
    line-height: 35rpx;
    display: flex;
    align-items: center;
    gap: 6rpx;

    .platform-feature-line {
      width: 1rpx;
      height: 19rpx;
      background: #4a5159;
    }
  }
}

.amount {
  position: relative;
  margin: 0 30rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/9e4d885c5d2e4c4b9d9ee3ba3688e1d0.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 45rpx 30rpx 30rpx;

  .title {
    font-weight: normal;
    font-size: 28rpx;
    color: #171a1d;
    line-height: 39rpx;
  }

  .input-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    input {
      font-family: DIN;
      font-weight: 700;
      font-size: 80rpx;
      color: #333333;
      line-height: 98rpx;
      flex: 1;
    }

    .all-btn {
      flex-shrink: 0;
      height: fit-content;
      padding: 17rpx 25rpx;
      background: $color-primary;
      box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.2);
      border-radius: 185rpx 185rpx 185rpx 185rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #eef9f3;
      line-height: 32rpx;
    }
  }

  .slider-container {
    :deep(.van-slider) {
      margin: 0;
    }

    .slider-tick {
      font-weight: 400;
      font-size: 24rpx;
      color: #747677;
      line-height: 35rpx;
      display: flex;
      justify-content: space-between;
    }
  }

  .annual-interest-rate {
    margin-top: 35rpx;
    display: flex;
    align-items: center;
    gap: 15rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    line-height: 32rpx;

    .tag {
      padding: 8rpx 18rpx;
      background: linear-gradient(270deg, #ffcd85 0%, #ffab44 100%);
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 25rpx;
      flex-shrink: 0;
      border-radius: 14rpx;
    }

    .highlight {
      color: #ffc93b;
    }
  }
}

.borrowing-options {
  padding: 15rpx 0;
  margin: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .option-line {
    margin-left: 60rpx;
    height: 2rpx;
    background-color: #f6f6f6;
  }

  .option {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    .label {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    &.term .value {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 41rpx;
      display: flex;
      align-items: center;

      .recommend {
        margin-left: 3rpx;
        padding: 7rpx 10rpx;
        background: #ff3636;
        border-radius: 203rpx 203rpx 203rpx 203rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #eef9f3;
        line-height: 18rpx;
      }
    }

    &.repayment-method .value {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;

      .amount-number {
        color: #333333;
      }

      .repayment-reminder {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 29rpx;
      }
    }

    &.coupon .value {
      gap: 15rpx;
      display: flex;
      align-items: center;

      .coupon-container {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 32rpx;
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .days {
          color: #edbf7c;
        }

        .coupon-tips {
          padding: 8rpx 11rpx;
          background: #fff0f5;
          border-radius: 8rpx 8rpx 8rpx 8rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #ff5e7c;
          line-height: 21rpx;
        }
      }
    }
  }
}

.phone-container {
  margin: 0 30rpx;
  padding: 40rpx 20rpx 36rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .phone-input {
    height: 96rpx;
    padding: 30rpx 40rpx;
    background: #f6f6f8;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
    font-size: 28rpx;
  }

  .get-my-quota {
    margin: 20rpx 0;
    padding: 22rpx;
    text-align: center;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    background: $color-primary;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
  }

  .agreement {
    display: flex;
    gap: 5rpx;

    .agree-icon {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
    }

    .agreement-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 32rpx;

      .name {
        color: $color-primary;
      }
    }
  }
}

.declaration {
  padding: 0 60rpx;
}

.agreement-popup {
  padding: 40rpx 50rpx 80rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/05f2447ceb6b45caaf742b8366205959.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
    //font-size: 26rpx;
    //color: #3D3D3D;
    //line-height: 42rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}

.code-popup {
  position: relative;
  padding: 60rpx 50rpx 80rpx;

  .close-btn {
    position: absolute;
    top: 9rpx;
    right: 9rpx;
    width: 48rpx;
    height: 48rpx;
  }

  .code-popup-title {
    margin-bottom: 50rpx;
    font-weight: 500;
    font-size: 38rpx;
    color: #333333;
    line-height: 55rpx;
    text-align: center;

    .highlight {
      color: #d4ad6a;
    }
  }

  .code-input-container {
    margin-bottom: 32rpx;
    height: 98rpx;
    display: flex;
    gap: 10rpx;
    align-items: center;
    font-size: 30rpx;
    line-height: 40rpx;
    padding: 30rpx 25rpx 30rpx 40rpx;
    background: #f5f5f5;
    border-radius: 49rpx 49rpx 49rpx 49rpx;

    input {
      flex: 1;
      font-size: 30rpx;
    }

    .gap-line {
      width: 1rpx;
      height: 40rpx;
      background-color: $color-primary;
    }

    .get-code-btn {
      min-width: 60rpx;
      font-weight: 400;
      font-size: 30rpx;
      color: $color-primary;
      line-height: 40rpx;
    }
  }

  .get-amount-btn {
    background: $color-primary;
    border-radius: 117rpx 117rpx 117rpx 117rpx;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    text-align: center;
    padding: 20rpx;
  }
}
</style>
