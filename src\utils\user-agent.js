/**
 * 判断当前环境是否为微信小程序
 * @returns {boolean} 如果是微信小程序环境返回true,否则返回false
 */
export function isInWechatMiniProgram() {
  const ua = window.navigator.userAgent.toLowerCase()
  return ua.includes('miniprogram') && ua.includes('micromessenger')
}

/**
 * 判断当前环境是否为微信浏览器
 * @returns {boolean} 如果是微信浏览器环境返回true,否则返回false
 */
export function isWeChatBrowser() {
  const ua = window.navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger') && !ua.includes('miniprogram')
}
