import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router'
import { createRouteObfuscation, createRouteUtils } from './obfuscate.js'
import { routeConfigs } from './routes.js'

// 创建路由混淆映射表
const { routeMap, reverseRouteMap } = createRouteObfuscation(routeConfigs)

// 自动生成routes配置
const routes = [
  // 添加根路径重定向到混淆后的首页
  {
    path: '/',
    redirect: routeMap['/']
  },
  // 生成混淆路由
  ...routeConfigs.map((config) => ({
    path: routeMap[config.originalPath],
    name: config.name,
    component: config.component,
    meta: { ...config.meta, originalPath: config.originalPath }
  }))
]

const router = createRouter({
  // history: createWebHistory(import.meta.env.BASE_URL),
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes
})

// 路由辅助函数
export const routeUtils = createRouteUtils(routeMap, reverseRouteMap)

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // 权限验证
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token')
    if (!token) {
      // 使用混淆后的路径进行跳转
      next(routeMap['/'] || '/')
      return
    }
  }
  next()
})

export default router
