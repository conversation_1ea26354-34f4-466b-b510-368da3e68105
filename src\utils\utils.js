/**
 *  @param { Function } 节流函数
 */
export function throttle(fn, interval) {
  let enterTime = 0 // 触发的时间
  let gapTime = interval || 300 // 间隔时间，如果interval不传，则默认300ms
  return function () {
    let context = this
    let backTime = new Date() // 第一次函数return即触发的时间
    if (backTime - enterTime > gapTime) {
      fn.call(context, ...arguments)
      enterTime = backTime // 赋值给第一次触发的时间，这样就保存了第二次触发的时间
    } else {
      uni.showToast({
        title: '亲!您点击的太快了呢',
        icon: 'none'
      })
    }
  }
}

/**
 * @param { Function } debounce 防抖函数
 */
export function debounce(func, wait, immediately) {
  let timer
  let debounced = function (...args) {
    let result
    // 清除闹钟后，闹钟还是存在的
    if (timer) clearTimeout(timer)
    if (immediately) {
      let called = !timer
      timer = setTimeout(() => {
        timer = null
      }, wait)
      if (called) {
        result = func.apply(this, args)
      }
    } else {
      timer = setTimeout(() => {
        func.apply(this, args)
      }, wait)
    }
    return result
  }
  debounced.cancel = function () {
    clearTimeout(timer)
    timer = null
  }
  return debounced
}

/**
 * 转换富文本的图片最大为100%
 * 转换行内样式的双引号问题
 */
export function formatRichText(html = '', info = {}) {
  let newContent = html

  newContent = newContent
    .replace(
      /#\{phone\}/gi,
      `<span style="color:red;text-decoration: underline;line-height:2.4;">${info.phone ? info.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''}</span>`
    )
    .replace(
      /#\{UUID\}/gi,
      `<span style="color:red;text-decoration: underline;line-height:2.4;">${info.appUserId || ''}</span>`
    )
    .replace(
      /#\{grantDateTime\}/gi,
      `<span style="color:red;text-decoration: underline;line-height:2.4;">${info.grantTime || ''}</span>`
    )
    .replace(
      /#\{name\}/gi,
      info.name ? `<span style="line-height:2.4;">${info.name || ''}</span>` : ''
    )
    .replace(
      /#\{idCard\}/gi,
      info.idCard
        ? `<span style="line-height:2.4;">${info.idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1******$2')}</span>`
        : ''
    )
    .replace(
      /#\{grantDateTime\}/gi,
      info.grantTime ? `<span style="line-height:2.4;">${info.grantTime || ''}</span>` : ''
    )
    .replace(/#\{organizationList\}/gi, info.institutions ? info.institutions : '')
  return newContent
}

/**
 *  @name 获取本地当前时间
 */
export function getNowDate() {
  let y = new Date().getFullYear()
  let m = new Date().getMonth() + 1
  let d = new Date().getDate()

  let hour = new Date().getHours()
  let min = new Date().getMinutes()
  let sec = new Date().getSeconds()

  let date = `${y}-${m < 10 ? '0' + m : m}-${d}`
  let time = `${hour < 10 ? '0' + hour : hour}:${min < 10 ? '0' + min : min}:${sec < 10 ? '0' + sec : sec}`

  return `${date} ${time}`
}
