import { onMounted, onUnmounted, ref } from 'vue'

/**
 * 页面可见性状态
 */
export const usePageVisibility = () => {
  const isVisible = ref(!document.hidden)
  
  const handleVisibilityChange = () => {
    isVisible.value = !document.hidden
  }
  
  onMounted(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange)
  })
  
  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  })
  
  return {
    isVisible
  }
}

/**
 * 页面显示时的回调 - 类似 uniapp onShow
 * @param {Function} callback - 页面显示时执行的回调函数
 * @param {Object} options - 配置选项
 * @param {boolean} options.immediate - 是否在组件挂载时立即执行一次，默认 true
 * @param {boolean} options.includeRouteChange - 是否包含路由变化触发，默认 false
 */
export const onShow = (callback, options = {}) => {
  const { immediate = true, includeRouteChange = false } = options
  
  if (typeof callback !== 'function') {
    console.warn('onShow: callback must be a function')
    return
  }
  
  let isPageVisible = !document.hidden
  let hasExecutedOnMount = false
  
  const handleVisibilityChange = () => {
    const currentlyVisible = !document.hidden
    
    // 从隐藏变为可见时触发
    if (!isPageVisible && currentlyVisible) {
      callback()
    }
    
    isPageVisible = currentlyVisible
  }
  
  const handleFocus = () => {
    if (!isPageVisible) {
      isPageVisible = true
      callback()
    }
  }
  
  onMounted(() => {
    // 组件挂载时立即执行一次（模拟页面首次显示）
    if (immediate && !hasExecutedOnMount) {
      hasExecutedOnMount = true
      callback()
    }
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // 监听窗口焦点变化（作为补充）
    window.addEventListener('focus', handleFocus)
    
    // 如果需要监听路由变化
    if (includeRouteChange && window.addEventListener) {
      window.addEventListener('popstate', callback)
    }
  })
  
  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('focus', handleFocus)
    
    if (includeRouteChange) {
      window.removeEventListener('popstate', callback)
    }
  })
}

/**
 * 页面隐藏时的回调 - 类似 uniapp onHide
 * @param {Function} callback - 页面隐藏时执行的回调函数
 * @param {Object} options - 配置选项
 * @param {boolean} options.includeBeforeUnload - 是否包含页面卸载前触发，默认 true
 */
export const onHide = (callback, options = {}) => {
  const { includeBeforeUnload = true } = options
  
  if (typeof callback !== 'function') {
    console.warn('onHide: callback must be a function')
    return
  }
  
  let isPageVisible = !document.hidden
  
  const handleVisibilityChange = () => {
    const currentlyVisible = !document.hidden
    
    // 从可见变为隐藏时触发
    if (isPageVisible && !currentlyVisible) {
      callback()
    }
    
    isPageVisible = currentlyVisible
  }
  
  const handleBlur = () => {
    if (isPageVisible) {
      isPageVisible = false
      callback()
    }
  }
  
  const handleBeforeUnload = () => {
    callback()
  }
  
  onMounted(() => {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // 监听窗口失去焦点
    window.addEventListener('blur', handleBlur)
    
    // 监听页面卸载前
    if (includeBeforeUnload) {
      window.addEventListener('beforeunload', handleBeforeUnload)
    }
  })
  
  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('blur', handleBlur)
    
    if (includeBeforeUnload) {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  })
}

/**
 * 组合使用 onShow 和 onHide
 * @param {Function} onShowCallback - 页面显示时的回调
 * @param {Function} onHideCallback - 页面隐藏时的回调
 * @param {Object} options - 配置选项
 */
export const usePageLifecycle = (onShowCallback, onHideCallback, options = {}) => {
  const { isVisible } = usePageVisibility()
  
  if (onShowCallback) {
    onShow(onShowCallback, options)
  }
  
  if (onHideCallback) {
    onHide(onHideCallback, options)
  }
  
  return {
    isVisible
  }
}

/**
 * 页面生命周期事件类型
 */
export const PAGE_LIFECYCLE_EVENTS = {
  SHOW: 'show',
  HIDE: 'hide',
  VISIBILITY_CHANGE: 'visibilitychange'
}

/**
 * 获取当前页面可见性状态
 */
export const getPageVisibilityState = () => {
  return {
    isVisible: !document.hidden,
    visibilityState: document.visibilityState,
    isHidden: document.hidden
  }
}
