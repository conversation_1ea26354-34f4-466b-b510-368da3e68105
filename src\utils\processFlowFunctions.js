import {
  fetchFormProduct,
  fetchHalfApiProduct,
  fetchOnlineProduct,
  fetchOutputProduct,
  fetchVirtualOverloanProduct,
  fetchFormOnlineProduct,
  fetchSmallLoanProduct
} from '@/apis/common-2'

const offline = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchFormProduct
  const res = await fetchFunc(params)
  if (res.code != 200) {
    return null
  }

  const result = {
    ...res.data,
    flow: 'offline'
  }

  if (res.data.productList) {
    // 排除 mid 等于 2 或者 3 的线上产品
    const productList = res.data.productList.filter((item) => item.mid != 2 && item.mid != 3)
    result.productList = productList
  }

  return result
}

const wechat = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchOnlineProduct
  const res = await fetchFunc({
    ...params,
    matchType: 1
    // limit: 1
  })
  if (res.code != 200) {
    return null
  }
  if (res.data.length) {
    return {
      flow: 'wechat',
      productList: res.data
    }
  }
}

const wechat_low = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchOnlineProduct
  const res = await fetchFunc({
    ...params,
    matchType: 1,
    matchLowWechatFlag: 1
  })
  if (res.code != 200) {
    return null
  }
  if (res.data.length) {
    return {
      flow: 'wechat_low',
      productList: res.data
    }
  }
}

const wechat_link = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchOnlineProduct
  const res = await fetchFunc({
    ...params,
    matchType: 1
    // limit: 1
  })
  if (res.code != 200) {
    return null
  }
  if (res.data.length) {
    return {
      flow: 'wechat_link',
      productList: res.data
    }
  }
}

const overloan = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchOnlineProduct
  const res = await fetchFunc({
    ...params,
    matchType: 2
    // limit: 1
  })
  if (res.code != 200) {
    return null
  }
  if (res.data.length) {
    return {
      flow: 'overloan',
      productList: res.data
    }
  }
}

const small_loan = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchSmallLoanProduct
  const res = await fetchFunc({
    ...params,
  })
  if (res.code != 200) {
    return null
  }
  if (res.data.length) {
    return {
      flow: 'small_loan',
      productList: res.data
    }
  }
}

const halfapi = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchHalfApiProduct
  const res = await fetchFunc(params)
  if (res.code != 200) {
    return null
  }
  if (res.data.length) {
    return {
      flow: 'halfapi',
      url: res.data
    }
  }
}

const output = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchOutputProduct
  const res = await fetchFunc(params)
  if (res.code != 200) {
    return null
  }

  if (res.data) {
    return {
      flow: 'output',
      productList: [res.data]
    }
  }
}

const virtual_overloan = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchVirtualOverloanProduct
  const res = await fetchFunc(params)
  if (res.code != 200) {
    return null
  }
  if (res.data.length) {
    return {
      flow: 'virtual_overloan',
      productList: res.data
    }
  }
}

const form_login = async (params, customFetch) => {
  const fetchFunc = customFetch || fetchFormOnlineProduct
  const res = await fetchFunc(params)
  if (res.code != 200) {
    return null
  }
  if (res.data.length) {
    return {
      flow: 'form_login',
      productList: res.data
    }
  }
}

const wechat_official_account = async (params, customFetch) => {
  return {
    flow: 'wechat_official_account',
    productList: []
  }
}

const user_update = async (params, customFetch) => {
  return {
    flow: 'user_update',
    productList: [
      {}
    ]
  }
}

// 移动到下一个节点
const flowHandlers = {
  offline,
  wechat,
  wechat_link,
  overloan,
  halfapi,
  output,
  virtual_overloan,
  form_login,
  wechat_official_account,
  user_update,
  wechat_low,
  small_loan
}

/**
 * 获取下一个流程节点
 * @param params {Object} 请求参数
 * @param customFetch {Object} 自定义请求函数对象，包含各个节点的自定义请求函数
 * @param customParams {Object} 自定义请求参数对象，包含各个节点的自定义请求参数
 * @returns {Object} 下一个流程节点
 */
export async function nextFlowNode(params, customFetch = {}, customParams = {}) {
  const flowNodes = uni.getStorageSync('flow') || []
  const flowIndex = uni.getStorageSync('flowIndex') || 0

  if (flowIndex >= flowNodes.length) {
    return {
      flow: 'end'
    }
  }

  for (let i = flowIndex; i < flowNodes.length; i++) {
    const nodeName = flowNodes[i]
    const nodeFunc = flowHandlers[nodeName]
    if (nodeFunc) {
      const nodeParams = customParams[nodeName] || {}
      const resultParams = { ...params, ...nodeParams }

      const result = await nodeFunc(resultParams, customFetch[nodeName])
      uni.setStorageSync('flowIndex', i + 1)
      if (result) {
        setFlowData(nodeName, result)
        return result
      } else {
        clearFlowData(nodeName)
        if (i === flowNodes.length - 1) {
          return {
            flow: 'end'
          }
        }
      }
    }
  }
}

const defaultFlow = ['offline', 'wechat', 'overloan', 'halfapi']

/**
 * 设置流程节点和索引
 * @param nodes 流程节点数组，不传则使用默认流程
 * @param index 流程索引
 */
export function setFlowNodes(nodes, index = 0) {
  if (!nodes) {
    nodes = defaultFlow
  }
  uni.setStorageSync('flow', nodes)
  uni.setStorageSync('flowIndex', index)
}

/**
 * 设置流程节点索引
 * @param index 流程索引
 */
export function setFlowIndex(index) {
  uni.setStorageSync('flowIndex', index)
}

/**
 * 清空 flowData
 * @param nodeName 节点名称
 */
export function clearFlowData(nodeName) {
  const flowData = uni.getStorageSync('flowData') || {}

  if (nodeName) {
    flowData[nodeName] = null
  } else {
    for (const key in flowData) {
      flowData[key] = null
    }
  }

  uni.setStorageSync('flowData', flowData)
}

/**
 * 流程数据
 * 格式：
 * {
 *   offline: {
 *     flow: 'offline',
 *     productList: []
 *   },
 *   wechat: {
 *     flow: 'wechat',
 *     productList: []
 *   },
 *   wechat_link: {
 *     flow: 'wechat_link',
 *     productList: []
 *   },
 *   overloan: {
 *     flow: 'overloan',
 *     productList: []
 *   },
 *   halfapi: {
 *     flow: 'halfapi',
 *     url: ''
 *   },
 *   output: {
 *     flow: 'output',
 *     productList: []
 *   }
 * }
 */

/**
 * 设置对应节点的数据
 * @param node 节点名称
 * @param data 数据
 */
export function setFlowData(node, data) {
  const flowData = uni.getStorageSync('flowData') || {}

  flowData[node] = data
  uni.setStorageSync('flowData', flowData)
}

/**
 * 获取对应节点的数据
 * @param nodeName 节点名称
 * @returns {Object | null}
 */
export function getFlowData(nodeName) {
  const flowData = uni.getStorageSync('flowData') || {}
  return flowData[nodeName] || null
}

/**
 * 获取指定类型的产品列表
 * @param nodeName 节点名称
 * @param params 请求参数
 * @param requestFunc 自定义请求函数
 * @returns {Promise<Object | null>}
 *
 * @example
 * getProductByFlow('wechat', {
 *   channelId: '123456',
 *   consumerId: '123456'
 * }, fetchOnlineProduct).then(res => {
 *   console.log(res)
 * })
 *
 */
export async function fetchProductByType(nodeName = '', params = {}, requestFunc = null) {
  const fn = flowHandlers[nodeName]

  if (fn) {
    const res = await fn(params, requestFunc)

    if (!res) {
      clearFlowData(nodeName)
      return null
    }

    setFlowData(nodeName, res)
    return res
  } else {
    clearFlowData(nodeName)
    return null
  }
}
