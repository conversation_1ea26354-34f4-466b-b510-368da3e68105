/**
 * 格式化金额为千分位
 * @param amount 金额，如 10000
 * @returns {string} 格式化后的金额，如 10,000
 */
export function formatAmount(amount) {
  if (String(amount).includes(',')) {
    return amount
  }

  return new Intl.NumberFormat('en-US').format(amount)
}

/**
 * 将千分位格式的金额转换为普通格式
 * @param amount 金额，如 10,000
 * @return {number} 普通格式的金额，如 10000
 */
export function parseAmount(amount) {
  if (typeof amount === 'number') {
    return amount
  }

  return Number(amount.replace(/,/g, ''))
}
