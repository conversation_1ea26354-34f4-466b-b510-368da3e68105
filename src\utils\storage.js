// 本地存储工具
class Storage {
  // 获取存储值
  get(key) {
    try {
      const value = localStorage.getItem(key)
      return value ? JSON.parse(value) : null
    } catch {
      return localStorage.getItem(key)
    }
  }

  // 设置存储值
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch {
      localStorage.setItem(key, value)
    }
  }

  // 移除存储值
  remove(key) {
    localStorage.removeItem(key)
  }

  // 清空存储
  clear() {
    localStorage.clear()
  }
}

// 会话存储工具
class SessionStorage {
  get(key) {
    try {
      const value = sessionStorage.getItem(key)
      return value ? JSON.parse(value) : null
    } catch {
      return sessionStorage.getItem(key)
    }
  }

  set(key, value) {
    try {
      sessionStorage.setItem(key, JSON.stringify(value))
    } catch {
      sessionStorage.setItem(key, value)
    }
  }

  remove(key) {
    sessionStorage.removeItem(key)
  }

  clear() {
    sessionStorage.clear()
  }
}

export const storage = new Storage()
export const sessionStorage = new SessionStorage()
