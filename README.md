# Vue3 + Vant H5 项目

基于 Vue3 + Vite + Vant 构建的移动端H5项目

## 技术栈

### 核心框架
- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 下一代前端构建工具
- **Vant 4** - 轻量、可靠的移动端组件库

### 开发工具
- **Vue Router 4** - Vue.js官方路由管理器
- **Pinia** - Vue的状态管理库
- **Axios** - HTTP客户端
- **Biome** - 快速的JavaScript/TypeScript格式化和检查工具
- **Sass/SCSS** - CSS预处理器

### 移动端适配
- **postcss-px-to-viewport-8-plugin** - px转vw/vh视口单位适配
- **viewport** - 视口适配方案
- **vmin/vmax** - 响应式视口单位支持

### 构建部署
- **Vite** - 快速构建打包
- **vite-plugin-compression2** - Gzip/Brotli资源压缩
- **Docker** - 容器化部署（可选）

## 项目目录结构

```
xm-h5/
├── public/                     # 静态资源文件
│   ├── favicon.ico
│   └── index.html
├── src/                        # 源代码目录
│   ├── apis/                   # API接口管理
│   │   ├── user.js
│   │   └── modules/
│   ├── assets/                 # 静态资源
│   │   ├── images/
│   │   ├── icons/
│   │   └── styles/
│   │       ├── index.scss      # 全局样式
│   │       └── mixins.scss     # 样式混入
│   ├── components/             # 公共组件
│   │   ├── common/             # 通用组件
│   │   └── business/           # 业务组件
│   ├── hooks/                  # Vue3组合式API钩子
│   │   ├── useRequest.js       # 请求钩子
│   │   ├── useStorage.js       # 存储钩子
│   │   └── useDevice.js        # 设备检测钩子
│   ├── pages/                  # 页面组件
│   │   ├── index/              # 首页模块
│   │   └── common/             # 公共页面
│   ├── router/                 # 路由配置
│   │   ├── index.js
│   │   └── modules/
│   ├── store/                  # 状态管理
│   │   ├── index.js
│   │   ├── modules/
│   │   │   ├── user.js
│   │   │   └── app.js
│   ├── utils/                  # 工具函数
│   │   ├── request.js          # 请求封装
│   │   ├── storage.js          # 存储工具
│   │   ├── device.js           # 设备工具
│   │   ├── date.js             # 日期工具
│   │   └── validator.js        # 验证工具
│   ├── App.vue                 # 根组件
│   └── main.js                 # 入口文件
├── .env                        # 环境变量
├── .env.development            # 开发环境变量
├── .env.production             # 生产环境变量
├── biome.json                  # Biome配置
├── .gitignore                  # Git忽略文件
├── index.html                  # HTML模板
├── package.json                # 依赖管理
├── postcss.config.js           # PostCSS配置
├── README.md                   # 项目说明
├── vite.config.js              # Vite配置
├── compression.config.js       # 压缩配置

```

## 功能模块规划

### 基础模块
- 用户认证（登录/注册/忘记密码）
- 个人中心
- 消息通知
- 设置页面

### 业务模块
- 首页展示
- 列表页面
- 详情页面
- 搜索功能

### 公共功能
- 图片预览
- 文件上传
- 下拉刷新
- 上拉加载
- 骨架屏
- 空状态页面
- 错误页面

## 开发规范

### 命名规范
- **文件命名**: kebab-case (短横线命名)
- **组件命名**: PascalCase (大驼峰)
- **变量/函数**: camelCase (小驼峰)
- **常量**: SCREAMING_SNAKE_CASE (全大写下划线)

### 代码规范
- 使用Biome进行代码格式化和检查
- 组件使用Composition API
- 遵循JavaScript ES6+语法规范
- 编写必要的注释和文档

### Git规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具变动

## 快速开始

### 环境要求
- Node.js >= 20.19
- npm >= 8

### 项目初始化
```bash
# 创建项目目录
mkdir xm-h5 && cd xm-h5

# 初始化项目
npm create vue@latest .

# 安装依赖
npm install

# 安装Vant组件库
npm install vant

# 安装移动端适配
npm install postcss-px-to-viewport-8-plugin --save-dev

# 安装压缩插件
npm install vite-plugin-compression2 --save-dev

# 安装Biome
npm install @biomejs/biome --save-dev

# 安装Sass
npm install sass --save-dev
```

### 安装依赖
```bash
npm install
```

### package.json脚本配置
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "check": "biome check ./src",
    "format": "biome format --write ./src",
    "fix": "biome check --write ./src"
  },
  "dependencies": {
    "vue": "^3.5.17",
    "vue-router": "^4.5.1",
    "pinia": "^3.0.3",
    "vant": "^4.9.20",
    "axios": "^1.11.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.2.1",
    "@biomejs/biome": "^2.1.2",
    "vite": "^7.0.6",
    "vite-plugin-compression2": "^2.2.0",
    "postcss-px-to-viewport-8-plugin": "^1.2.5",
    "sass": "^1.89.2"
  }
}
```

### 开发调试
```bash
npm run dev
```

### 构建打包
```bash
npm run build
```

### 代码检查和格式化
```bash
# 检查代码规范
npm run check

# 格式化代码
npm run format

# 自动修复问题
npm run fix
```

## 配置文件示例

### Biome配置 (biome.json)
```json
{
  "$schema": "https://biomejs.dev/schemas/1.4.1/schema.json",
  "organizeImports": {
    "enabled": true
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true
    }
  },
  "formatter": {
    "enabled": true,
    "formatWithErrors": false,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineWidth": 100,
    "lineEnding": "lf"
  },
  "javascript": {
    "formatter": {
      "quoteStyle": "single",
      "semicolons": "asNeeded",
      "trailingComma": "es5"
    }
  }
}
```

### 环境变量配置
```bash
# .env
VITE_APP_TITLE=XM H5项目
VITE_APP_BASE_API=/api

# .env.development
VITE_APP_BASE_URL=http://localhost:8080
VITE_APP_ENV=development

# .env.production
VITE_APP_BASE_URL=https://api.example.com
VITE_APP_ENV=production
```

### Vite完整配置 (vite.config.js)
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { compression } from 'vite-plugin-compression2'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    // Gzip压缩
    compression({
      algorithm: 'gzip',
      ext: '.gz',
      threshold: 1024,
      deleteOriginalAssets: false
    }),
    // Brotli压缩
    compression({
      algorithm: 'brotliCompress',
      ext: '.br',
      threshold: 1024
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        // SCSS 预处理器选项
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
```

### PostCSS配置 (postcss.config.js)
```javascript
module.exports = {
  plugins: {
    'postcss-px-to-viewport-8-plugin': {
      unitToConvert: 'px',
      viewportWidth: 375,
      unitPrecision: 5,
      propList: ['*'],
      viewportUnit: 'vw',
      fontViewportUnit: 'vw',
      selectorBlackList: ['.ignore-vw'],
      minPixelValue: 1,
      mediaQuery: false,
      replace: true,
      exclude: [/node_modules/]
    }
  }
}
```

## 核心配置示例

### Pinia状态管理
```javascript
// src/store/index.js
import { createPinia } from 'pinia'

const pinia = createPinia()
export default pinia

// src/store/modules/user.js
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: localStorage.getItem('token') || ''
  }),
  
  getters: {
    isLogin: (state) => !!state.token
  },
  
  actions: {
    setToken(token) {
      this.token = token
      localStorage.setItem('token', token)
    },
    
    setUserInfo(userInfo) {
      this.userInfo = userInfo
    },
    
    logout() {
      this.token = ''
      this.userInfo = null
      localStorage.removeItem('token')
    }
  }
})
```

### Vue Router配置
```javascript
// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/pages/index/index.vue'),
    meta: { title: '首页' }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token')
    if (!token) {
      next('/login')
      return
    }
  }
  next()
})

export default router
```

### 主入口文件 (src/main.js)
```javascript
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './store'

// Vant组件库
import { Button, Toast, Dialog } from 'vant'
import 'vant/lib/index.css'

// 全局样式
import '@/assets/styles/index.scss'

const app = createApp(App)

// 注册Vant组件
app.use(Button)
app.use(Toast)
app.use(Dialog)

app.use(router)
app.use(pinia)

app.mount('#app')
```

### vw/vh视口适配方案
- 设计稿宽度: 375px
- 1vw = 3.75px (100vw = 375px)
- 使用postcss-px-to-viewport-8-plugin自动转换px为vw
- 支持vmin/vmax单位实现更灵活的响应式设计

### postcss-px-to-viewport-8-plugin配置
```javascript
{
  unitToConvert: 'px',
  viewportWidth: 375,
  unitPrecision: 5,
  propList: ['*'],
  viewportUnit: 'vw',
  fontViewportUnit: 'vw',
  selectorBlackList: [],
  minPixelValue: 1,
  mediaQuery: false,
  replace: true
}
```

### 视口配置
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
```

### 1px边框问题
使用Vant内置的1px解决方案

## 构建优化

### Gzip压缩配置
使用vite-plugin-compression2插件实现资源压缩：

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import { compression } from 'vite-plugin-compression2'

export default defineConfig({
  plugins: [
    // Gzip压缩
    compression({
      algorithm: 'gzip',
      ext: '.gz',
      threshold: 1024, // 只压缩大于1KB的文件
      deleteOriginalAssets: false
    }),
    // Brotli压缩（可选）
    compression({
      algorithm: 'brotliCompress',
      ext: '.br',
      threshold: 1024
    })
  ]
})
```

### 构建产物优化
- **代码分割**：自动分离第三方库和业务代码
- **Tree Shaking**：移除未使用的代码
- **资源压缩**：JS/CSS/HTML自动压缩
- **图片优化**：支持WebP格式转换

## 部署说明

### 静态部署
构建后将dist目录部署到静态服务器

### Docker部署
```dockerfile
FROM nginx:alpine
COPY dist /usr/share/nginx/html
EXPOSE 80
```

## 注意事项

1. 移动端兼容性测试
2. 不同设备屏幕适配
3. 网络状态处理
4. 用户体验优化
5. 性能监控和优化

## 技术支持

如有问题请联系开发团队或查看相关文档。