import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { compression } from 'vite-plugin-compression2'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    // Gzip压缩
    compression({
      algorithm: 'gzip',
      ext: '.gz',
      threshold: 1024,
      deleteOriginalAssets: false
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        // SCSS 预处理器选项
      }
    }
  },
  build: {
    // 提高chunk size警告阈值
    chunkSizeWarningLimit: 1000,
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 启用构建缓存
    emptyOutDir: true,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
        // 手动分割chunks
        manualChunks: {
          // 将Vue相关依赖打包到vendor
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // 将Vant UI库单独打包
          'vant-vendor': ['vant']
        }
      },
      // 外部化依赖优化（暂未使用）
      // external: (id) => false
    },
    // 压缩选项
    minify: 'terser',
    terserOptions: {
      compress: {
        // 移除console
        drop_console: true,
        // 移除debugger
        drop_debugger: true,
        // 移除注释
        pure_funcs: ['console.log']
      }
    }
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})