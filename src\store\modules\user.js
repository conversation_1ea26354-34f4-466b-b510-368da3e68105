import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 广告来源
    vuex_adReferrer: {},
    // 模板中的webviewDown页面需要的数据
    vuex_webviewDown: {
      url: ''
    },
    // 用于测试产品的模板，会用到linkId
    vuex_linkId: '',
    // 页面底部的声明文案
    vuex_declaration: '',
    // url query 参数中的 templateSig 参数，指定模板类型，一般用于测试
    vuex_templateSig: '',
    // 用户id
    vuex_uid: '',
    // 半流程跳转外链
    vuex_halfJumpUrl: '',
    // 贷超产品webview 参数
    vuex_overloanWebview: {
      url: ''
    },
    // 大联登产品webview 参数
    vuex_formLoginWebview: {
      url: ''
    },
    // 短信ID
    vuex_smsId: '',
    // 邀请码
    vuex_invite: '',
    // 模板主题
    vuex_theme: '',
    // 模板配置信息
    vuex_templateConfig: {},
    // 渠道ID
    vuex_channelId: '',
    // 首页URL
    vuex_homePageUrl: '',
    // 用户手机号
    vuex_phone: '',
    // 消费者ID
    vuex_consumerId: '',
    // 是否在微信小程序中
    vuex_isInWechatMiniProgram: false,
    // 外链平台ID
    vuex_outboundPlatformId: ''
  }),

  getters: {
    // 可以在这里添加其他计算属性
  },

  actions: {
    // 设置广告来源
    setAdReferrer(data) {
      this.vuex_adReferrer = data
    },

    // 设置webview下载页面数据
    setWebviewDown(data) {
      this.vuex_webviewDown = data
    },

    // 设置链接ID
    setLinkId(linkId) {
      this.vuex_linkId = linkId
    },

    // 设置声明文案
    setDeclaration(declaration) {
      this.vuex_declaration = declaration
    },

    // 设置模板签名
    setTemplateSig(templateSig) {
      this.vuex_templateSig = templateSig
    },

    // 设置用户ID
    setUid(uid) {
      this.vuex_uid = uid
    },

    // 设置半流程跳转URL
    setHalfJumpUrl(url) {
      this.vuex_halfJumpUrl = url
    },

    // 设置贷超产品webview参数
    setOverloanWebview(data) {
      this.vuex_overloanWebview = data
    },

    // 设置大联登产品webview参数
    setFormLoginWebview(data) {
      this.vuex_formLoginWebview = data
    },

    // 设置短信ID
    setSmsId(smsId) {
      this.vuex_smsId = smsId
    },

    // 设置邀请码
    setInvite(invite) {
      this.vuex_invite = invite
    },

    // 设置模板主题
    setTheme(theme) {
      this.vuex_theme = theme
    },

    // 设置模板配置信息
    setTemplateConfig(config) {
      this.vuex_templateConfig = config
    },

    // 设置渠道ID
    setChannelId(channelId) {
      this.vuex_channelId = channelId
    },

    // 设置首页URL
    setHomePageUrl(url) {
      this.vuex_homePageUrl = url
    },

    // 设置用户手机号
    setPhone(phone) {
      this.vuex_phone = phone
    },

    // 设置消费者ID
    setConsumerId(consumerId) {
      this.vuex_consumerId = consumerId
    },

    // 设置是否在微信小程序中
    setIsInWechatMiniProgram(isIn) {
      this.vuex_isInWechatMiniProgram = isIn
    },

    // 设置外链平台ID
    setOutboundPlatformId(platformId) {
      this.vuex_outboundPlatformId = platformId
    }
  },

  persist: true
})
