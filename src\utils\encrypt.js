import CryptoJS from 'crypto-js'

const secretKey = 'com.xcyd.app.des20121' // 加密秘钥
let mode = 'ECB' // 加密方式
let pad = 'Pkcs7' // 填充方式

/**
 * 加密
 */
export function encryptByDES(message) {
  const keyHex = CryptoJS.enc.Utf8.parse(secretKey)
  const encrypted = CryptoJS.DES.encrypt(message, keyHex, {
    mode: CryptoJS.mode[mode],
    padding: CryptoJS.pad[pad]
  })
  return encrypted.toString()
}

/**
 * 解密
 */
export function decryptByDES(ciphertext) {
  const keyHex = CryptoJS.enc.Utf8.parse(secretKey)
  const decrypted = CryptoJS.DES.decrypt(
    {
      ciphertext: CryptoJS.enc.Base64.parse(ciphertext)
    },
    keyHex,
    {
      mode: CryptoJS.mode[mode],
      padding: CryptoJS.pad[pad]
    }
  )
  return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8))
}

/**
 * 解密字符串
 */
export function decryptStr(ciphertext) {
  const keyHex = CryptoJS.enc.Utf8.parse(secretKey)
  const decrypted = CryptoJS.DES.decrypt(
    {
      ciphertext: CryptoJS.enc.Base64.parse(ciphertext)
    },
    keyHex,
    {
      mode: CryptoJS.mode[mode],
      padding: CryptoJS.pad[pad]
    }
  )
  return decrypted.toString(CryptoJS.enc.Utf8)
}
