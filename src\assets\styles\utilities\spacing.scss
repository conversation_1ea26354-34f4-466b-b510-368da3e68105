// 间距工具类 - 使用 SCSS 循环生成

// 间距数值映射
$spacing-values: (
  0: 0,
  1: 4px,
  2: 8px,
  3: 12px,
  4: 16px,
  5: 20px,
  6: 24px,
  8: 32px,
  10: 40px,
  12: 48px,
  16: 64px,
  20: 80px
);

// 生成 Padding 类
@each $key, $value in $spacing-values {
  .p-#{$key} { padding: $value; }
  
  .px-#{$key} { 
    padding-left: $value; 
    padding-right: $value; 
  }
  
  .py-#{$key} { 
    padding-top: $value; 
    padding-bottom: $value; 
  }
  
  .pt-#{$key} { padding-top: $value; }
  .pr-#{$key} { padding-right: $value; }
  .pb-#{$key} { padding-bottom: $value; }
  .pl-#{$key} { padding-left: $value; }
}

// 生成 Margin 类
@each $key, $value in $spacing-values {
  .m-#{$key} { margin: $value; }
  
  .mx-#{$key} { 
    margin-left: $value; 
    margin-right: $value; 
  }
  
  .my-#{$key} { 
    margin-top: $value; 
    margin-bottom: $value; 
  }
  
  .mt-#{$key} { margin-top: $value; }
  .mr-#{$key} { margin-right: $value; }
  .mb-#{$key} { margin-bottom: $value; }
  .ml-#{$key} { margin-left: $value; }
}

// Auto margin
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }
.mt-auto { margin-top: auto; }
.mr-auto { margin-right: auto; }
.mb-auto { margin-bottom: auto; }
.ml-auto { margin-left: auto; }

// 负边距（常用的几个）
$negative-spacing: (
  1: -4px,
  2: -8px,
  3: -12px,
  4: -16px
);

@each $key, $value in $negative-spacing {
  .-m-#{$key} { margin: $value; }
  
  .-mx-#{$key} { 
    margin-left: $value; 
    margin-right: $value; 
  }
  
  .-my-#{$key} { 
    margin-top: $value; 
    margin-bottom: $value; 
  }
  
  .-mt-#{$key} { margin-top: $value; }
  .-mr-#{$key} { margin-right: $value; }
  .-mb-#{$key} { margin-bottom: $value; }
  .-ml-#{$key} { margin-left: $value; }
}