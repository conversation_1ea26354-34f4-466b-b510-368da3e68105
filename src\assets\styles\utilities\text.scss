// 文本工具类 - 参考 Tailwind CSS 设计

// Text Alignment
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

// Font Size
.text-xs {
  font-size: 12px;
  line-height: 16px;
}

.text-sm {
  font-size: 14px;
  line-height: 20px;
}

.text-base {
  font-size: 16px;
  line-height: 24px;
}

.text-lg {
  font-size: 18px;
  line-height: 28px;
}

.text-xl {
  font-size: 20px;
  line-height: 28px;
}

.text-2xl {
  font-size: 24px;
  line-height: 32px;
}

.text-3xl {
  font-size: 30px;
  line-height: 36px;
}

.text-4xl {
  font-size: 36px;
  line-height: 40px;
}

// Font Weight
.font-thin {
  font-weight: 100;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
}

// Text Color
.text-black {
  color: #000000;
}

.text-white {
  color: #ffffff;
}

.text-gray-50 {
  color: #fafafa;
}

.text-gray-100 {
  color: #f5f5f5;
}

.text-gray-200 {
  color: #eeeeee;
}

.text-gray-300 {
  color: #e0e0e0;
}

.text-gray-400 {
  color: #bdbdbd;
}

.text-gray-500 {
  color: #9e9e9e;
}

.text-gray-600 {
  color: #757575;
}

.text-gray-700 {
  color: #616161;
}

.text-gray-800 {
  color: #424242;
}

.text-gray-900 {
  color: #212121;
}

// 常用颜色（移动端常用）
.text-primary {
  color: #1989fa;
}

.text-success {
  color: #07c160;
}

.text-warning {
  color: #ff976a;
}

.text-danger {
  color: #ee0a24;
}

.text-info {
  color: #1989fa;
}

// 文本默认颜色
.text-default {
  color: #323233;
}

.text-secondary {
  color: #646566;
}

.text-tertiary {
  color: #969799;
}

.text-disabled {
  color: #c8c9cc;
}

// Line Height
.leading-none {
  line-height: 1;
}

.leading-tight {
  line-height: 1.25;
}

.leading-snug {
  line-height: 1.375;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-loose {
  line-height: 2;
}

// Text Decoration
.underline {
  text-decoration: underline;
}

.line-through {
  text-decoration: line-through;
}

.no-underline {
  text-decoration: none;
}

// Text Transform
.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.normal-case {
  text-transform: none;
}

// Text Overflow
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-clip {
  text-overflow: clip;
}

// White Space
.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre {
  white-space: pre;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

// Word Break
.break-normal {
  overflow-wrap: normal;
  word-break: normal;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

// 多行文本省略
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}