export default {
  plugins: {
    'postcss-px-to-viewport-8-plugin': {
      unitToConvert: 'px',
      viewportWidth: 375,
      unitPrecision: 5,
      propList: ['*'],
      viewportUnit: 'vw',
      fontViewportUnit: 'vw',
      selectorBlackList: ['.ignore-vw'],
      minPixelValue: 1,
      mediaQuery: false,
      replace: true,
      // 确保包含Vant组件库的样式文件
      include: [
        /\.vue$/,
        /\.scss$/,
        /\.css$/,
        /node_modules\/vant/
      ],
      // 排除其他node_modules包
      exclude: [
        /node_modules\/(?!vant)/
      ]
    }
  }
}