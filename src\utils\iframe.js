/**
 * iframe相关工具方法
 */

/**
 * 检测当前页面是否被嵌套在iframe中 (方法1)
 * 通过比较window.self和window.top
 * @returns {boolean} true表示被嵌套,false表示未被嵌套
 */
export function checkIframeNestingBySelfTop() {
  return window.self !== window.top
}

/**
 * 检测当前页面是否被嵌套在iframe中 (方法2)
 * 通过比较window和window.parent
 * @returns {boolean} true表示被嵌套,false表示未被嵌套
 */
export function checkIframeNestingByParent() {
  return window !== window.parent
}

/**
 * 检测当前页面是否被嵌套在iframe中 (方法3)
 * 通过检测window.frameElement
 * @returns {boolean} true表示被嵌套,false表示未被嵌套
 */
export function checkIframeNestingByFrameElement() {
  return !!window.frameElement
}

/**
 * 综合检测当前页面是否被嵌套在iframe中
 * 使用三种方法都检测一遍,只要一种方法检测到就返回true
 * @returns {boolean} true表示被嵌套,false表示未被嵌套
 */
export function isPageNested() {
  return (
    checkIframeNestingBySelfTop() ||
    checkIframeNestingByParent() ||
    checkIframeNestingByFrameElement()
  )
}

/**
 * 获取当前页面的嵌套层级
 * @returns {number} 返回嵌套层级数,0表示未被嵌套
 */
export function getIframeNestingLevel() {
  let level = 0
  let currentWindow = window

  while (currentWindow !== window.top) {
    level++
    currentWindow = currentWindow.parent
  }

  return level
}

/**
 * 阻止页面被iframe嵌套
 * 如果检测到被嵌套,则将页面重定向到顶级窗口
 */
export function preventIframeNesting() {
  if (isPageNested()) {
    window.top.location.href = window.self.location.href
  }
}
