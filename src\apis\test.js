import { $get, $post } from '@/utils/request.js'

// 根据邀请码获取渠道号和模板
export const fetchChannelAndTemplate = (data = {}) => {
  return $get({
    url: `/test/link/verify/${data.linkId}`,
    isEncrypt: false,
    deEncrypt: false
  })
}

export const fetchFlow = (data = {}) => {
  return $get({
    url: '/test/link/sequence',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配半 api 产品
export const fetchHalfApiProduct = (data = {}) => {
  return $post({
    url: '/test/link/matching/half',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配线上产品
export const fetchOnlineProduct = (data = {}) => {
  return $post({
    url: '/test/link/matching/online',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配出量产品
export const fetchOutputProduct = (data = {}) => {
  return $post({
    url: '/test/link/matching/output',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 直接注册手机号，无需验证码
export const fetchDirectRegister = (data = {}) => {
  return $post({
    url: '/user/test/link/saveLoan',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 添加当天申请的产品id
export const addNowDayApplyProductId = (data = {}) => {
  return $get({
    url: '/test/link/addNowDayApplyProductId',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 返回当天申请的产品id
export const getNowDayAlreadyApplyProductId = (data = {}) => {
  return $get({
    url: '/test/link/nowDayAlreadyApplyProductId',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请在线链接
export const applyOnline = (data = {}) => {
  return $post({
    url: '/test/link/applyOnline',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}
