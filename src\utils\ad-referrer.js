import { ad_referrer } from '@/const/ad_referrer'

/**
 * 从查询参数中获取广告引荐来源信息
 * @param {Object} query - 包含查询参数的对象
 * @returns {Object|null} 返回包含广告引荐键值对的对象，如果未找到则返回null
 * @property {string} key - 广告引荐参数的键名
 * @property {string} value - 广告引荐参数的值
 */
export function getAdReferrer(query) {
  for (const key of ad_referrer) {
    if (query.hasOwnProperty(key)) {
      return { key, value: query[key] }
    }
  }
  return null
}
