<template>
  <div>
    <component :is="currentTemplate" :channelId="channelId"> </component>

    <van-dialog
      v-model:show="pass.show"
      title=""
      message="亲！请通过正常渠道访问哟"
      :show-cancel-button="false"
      :close-on-click-overlay="false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { fetchChannelAndTemplate } from '@/apis/common'
import { fetchChannelAndTemplate as fetchChannelAndTemplateTest } from '@/apis/test'
import { clearBlackPhone } from '@/utils/black-phone'
import { useUserStore } from '@/store/modules/user'
import { storeToRefs } from 'pinia'

const templateMap = {
  v66: () => import('@/components/template/v66.vue'),
  // v67: () => import('@/components/template/v67'),
}

// 响应式数据
const channelId = ref('')
const templateModel = ref('')
const invite = ref('')
const pass = ref({
  show: false
})

// 使用 Vue Router
const route = useRoute()

// 使用 Pinia store
const userStore = useUserStore()
const { vuex_linkId, vuex_templateSig } = storeToRefs(userStore)

// 计算属性
const currentTemplate = computed(() => {
  return templateMap[templateModel.value]
})

// 保存当前页面URL到user store
const saveHomePageUrl = () => {
  const currentUrl = window.location.href
  userStore.setHomePageUrl(currentUrl)
}

const fetchChannelAndTemplateMethod = async () => {
  let res = null

  if (vuex_linkId.value) {
    res = await fetchChannelAndTemplateTest({
      linkId: vuex_linkId.value
    })
  } else {
    res = await fetchChannelAndTemplate({
      invite: invite.value
    })
  }

  if (res.code != 200) {
    userStore.setDeclaration('协议获取失败，请稍后再试')
    return
  }

  channelId.value = res.data.channelId
  userStore.setChannelId(res.data.channelId)
  userStore.setDeclaration(res.data.html)

  if (vuex_templateSig.value) {
    templateModel.value = vuex_templateSig.value
  } else {
    templateModel.value = res.data.sig || 'v5'
  }
}

// 使用 onMounted 替代 onLoad
onMounted(() => {
  // 使用 Vue Router 获取 query 参数
  const { i, templateSig, linkId } = route.query

  userStore.setTemplateSig(templateSig)
  userStore.setLinkId(linkId)

  // 调用保存当前URL的方法
  saveHomePageUrl()

  if (!i && !vuex_linkId.value && !vuex_templateSig.value) {
    pass.value.show = true
    return
  }

  invite.value = i

  if (vuex_linkId.value) {
    clearBlackPhone()
  }

  userStore.setDeclaration('正在加载协议')
  uni.setStorageSync('invite', invite.value)

  if (invite.value || vuex_linkId.value) {
    fetchChannelAndTemplateMethod()
  } else if (vuex_templateSig.value) {
    templateModel.value = vuex_templateSig.value
  }
})
</script>

<style lang="scss" scoped>
</style>
