import { $get, $post } from '@/utils/request'
import queryParams from '@/utils/queryParams'

// 注册，发送短信验证码
export const sendSmsCode = (data = {}) => {
  return $post({
    url: `/infoFlow/form/send${queryParams(data)}`,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// uv统计
export const reportUV = (data = {}) => {
  return $get({
    url: '/infoFlow/form/8a848a',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请表单产品
export const applyFormProduct = (data = {}) => {
  return $post({
    url: '/infoFlow/form/applyProducts',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请线上产品
export const applyOnlineProduct = (data = {}) => {
  return $post({
    url: '/infoFlow/form/applyOnline',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配表单产品
export const fetchFormProduct = (data = {}) => {
  return $post({
    url: '/infoFlow/form/matching',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配线上产品
export const fetchOnlineProduct = (data = {}) => {
  return $post({
    url: '/infoFlow/form/matching/online',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配半 api 产品
export const fetchHalfApiProduct = (data = {}) => {
  return $post({
    url: '/infoFlow/form/matching/half',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 保存用户资质
export const saveUserInfo = (data = {}) => {
  return $post({
    url: '/infoFlow/form/consumerAssetsUpdate',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 保存小程序打开记录
export const saveAppletOpenRecord = (data = {}) => {
  return $get({
    url: '/temp/saveAppletOpenRecord',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 保存页面嵌套打开记录
export const savePageNestingOpenRecord = (data = {}) => {
  return $post({
    url: '/temp/savePageNestingOpenRecord',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}
