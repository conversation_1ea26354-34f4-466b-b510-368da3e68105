// 首页模块路由
const homeRoutes = [
  {
    originalPath: '/',
    name: 'Home',
    component: () => import('@/pages/index/index.vue'),
    meta: { title: '首页' }
  }
]

// 用户模块路由（已删除）
const userRoutes = []

// 示例：产品模块路由（未来可以添加）
// const productRoutes = [
//   {
//     originalPath: '/product',
//     name: 'ProductList',
//     component: () => import('@/pages/product/index.vue'),
//     meta: { title: '产品列表' }
//   },
//   {
//     originalPath: '/product/:id',
//     name: 'ProductDetail',
//     component: () => import('@/pages/product/detail.vue'),
//     meta: { title: '产品详情' }
//   }
// ]

// 示例：订单模块路由（未来可以添加）
// const orderRoutes = [
//   {
//     originalPath: '/order',
//     name: 'OrderList',
//     component: () => import('@/pages/order/index.vue'),
//     meta: { title: '订单列表', requiresAuth: true }
//   }
// ]

// 合并所有路由配置
export const routeConfigs = [
  ...homeRoutes,
  ...userRoutes
  // ...productRoutes,  // 取消注释以启用产品路由
  // ...orderRoutes     // 取消注释以启用订单路由
]

// 导出各个模块的路由配置，方便单独管理和测试
export {
  homeRoutes,
  userRoutes
  // productRoutes,
  // orderRoutes
}
