import { getProtocolList, getProtocolDetail } from '@/apis/common-2';
import dayjs from 'dayjs';

/**
 * 替换富文本字符串中的占位符。
 * @param {string} richTextString - 包含占位符的富文本字符串。
 * @param {object} replacements - 一个对象，其中键是要查找的占位符，值是替换内容。
 * @returns {string} - 替换占位符后的富文本字符串。
 * @example
 * const richText = "<p>你好 #{name}，欢迎来到 #{platform}！</p><p>你的用户ID是：#{userId}</p>";
 * const data = {
 *   "name": "张三",
 *   "platform": "我们的应用",
 *   "userId": "12345"
 * };
 * const processedText = replacePlaceholders(richText, data);
 * console.log(processedText);
 * // 输出： "<p>你好 张三，欢迎来到 我们的应用！</p><p>你的用户ID是：12345</p>"
 */
export function replacePlaceholders(richTextString, replacements) {
  if (typeof richTextString !== 'string') {
    console.error('错误：richTextString 参数必须是字符串。');
    return richTextString; // 或者可以抛出错误 throw new Error('richTextString must be a string');
  }
  if (typeof replacements !== 'object' || replacements === null) {
    console.error('错误：replacements 参数必须是一个对象。');
    return richTextString; // 或者可以抛出错误 throw new Error('replacements must be an object');
  }

  let result = richTextString;
  for (const key in replacements) {
    if (Object.prototype.hasOwnProperty.call(replacements, key)) {
      // 构建占位符，例如：将 "name" 转换为 "#{name}"
      const placeholder = `#{${key}}`;
      result = result.replaceAll(placeholder, replacements[key]);
    }
  }
  return result;
}


/**
 * 将协议keys转换为字符串格式
 * @param {string | string[]} agreementKeys - 协议的 key 或 key 数组
 * @returns {string} 转换后的字符串
 */
function normalizeAgreementKeys(agreementKeys) {
  if (Array.isArray(agreementKeys)) {
    return agreementKeys.join(',');
  }
  if (typeof agreementKeys === 'string') {
    return agreementKeys;
  }
  console.error('错误：agreementKeys 参数必须是字符串或字符串数组。');
  return '';
}

/**
 * 获取单个协议的详细内容
 * @param {object} agreement - 协议基本信息
 * @param {object} replacements - 替换内容
 * @returns {Promise<object>} 处理后的协议对象
 */
async function fetchAndProcessAgreementDetail(agreement, replacements) {
  if (!agreement || typeof agreement.protocolId === 'undefined') {
    console.warn('跳过无效的协议对象:', agreement);
    return null;
  }

  try {
    const detailRes = await getProtocolDetail({ protocolId: agreement.protocolId });
    const rawContent = detailRes.data?.content ?? '';
    const processedContent = replacePlaceholders(rawContent, replacements);

    return {
      protocolId: agreement.protocolId,
      name: agreement.name || '未知协议',
      key: agreement.key,
      content: processedContent,
    };
  } catch (detailError) {
    console.error(`获取或处理协议ID ${agreement.protocolId} 的详情时出错:`, detailError);
    return {
      protocolId: agreement.protocolId,
      name: agreement.name || '未知协议 (加载失败)',
      key: agreement.key,
      content: '协议内容加载失败',
    };
  }
}


function getDefaultsReplacements() {
  const lifeData = uni.getStorageSync('lifeData')

  return {
    phone: lifeData.vuex_phone || '',
    UUID: lifeData.vuex_consumerId || '',
    grantDateTime: dayjs().format('YYYY-MM-DD')
  }
}

/**
 * 获取并处理用户协议数据。
 * @param {string | string[]} agreementKeys - 协议的 key 或 key 数组。如果是字符串，则为逗号分隔的 keys。
 * @param {object} [replacements={}] - 一个对象，其中键是要查找的占位符，值是替换内容。
 * @returns {Promise<Array<object>>} - 处理后的用户协议对象数组，每个对象包含 protocolId, name, key, content。
 * @example
 * const keys = ['user_agreement_key', 'privacy_policy_key'];
 * const data = { userName: "张三" };
 * const agreements = await getAgreements(keys, data);
 * // agreements: [
 * //   { protocolId: 1, name: "用户协议", key: "user_agreement_key", content: "<p>你好 张三...</p>" },
 * //   { protocolId: 2, name: "隐私政策", key: "privacy_policy_key", content: "<p>你的隐私 张三...</p>" }
 * // ]
 */
export async function getAgreements(agreementKeys, replacements = {}) {
  const agreementKeysString = normalizeAgreementKeys(agreementKeys);
  if (!agreementKeysString) {
    return [];
  }

  // 检查replacements是否有效，如果无效则使用默认值
  const validReplacements = replacements && Object.keys(replacements).length > 0 ? replacements : getDefaultsReplacements();

  try {
    const agreementListRes = await getProtocolList({ protocolSig: agreementKeysString });
    const rawAgreementList = agreementListRes.data || [];

    if (!Array.isArray(rawAgreementList)) {
      console.error('错误：getProtocolList 未返回有效的数组。');
      return [];
    }

    const processedAgreements = await Promise.all(
      rawAgreementList.map(agreement => 
        fetchAndProcessAgreementDetail(agreement, validReplacements)
      )
    );

    return processedAgreements.filter(p => p !== null);
  } catch (listError) {
    console.error('获取协议列表时出错:', listError);
    return [];
  }
} 