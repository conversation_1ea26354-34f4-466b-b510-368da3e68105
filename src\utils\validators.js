//验证手机号
export function validateMobile(mobile) {
  const regExp = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
  return regExp.test(mobile)
}
//验证是否空白
export function isEmpty(val) {
  // eslint-disable-next-line
  return val === '' || val == null || val == undefined
}
//验证ID
export function validateID(id) {
  if (!id) return false
  if (id.length === 18) {
    let regExp =
      /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/
    return regExp.test(id)
  }
  if (id.length === 15) {
    let regExp = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/
    return regExp.test(id)
  }
  return false
}
//验证姓名
export function validateName(name) {
  let regExp = /^([\u4e00-\u9fa5]{2,6})((\·[\u4e00-\u9fa5]){1,6})?$/
  return regExp.test(name)
}

// 验证数字
export function validateNumber(num) {
  return /^\+?[1-9][0-9]*$/.test(num)
}
