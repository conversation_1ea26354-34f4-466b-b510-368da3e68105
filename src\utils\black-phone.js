import { decryptStr, encryptByDES } from './encrypt.js'

export function getBlackPhone(
  options = {
    limit: 2
  }
) {
  const storagePhoneEncryption = uni.getStorageSync('bp')
  const storagePhoneDecryption = storagePhoneEncryption ? decryptStr(storagePhoneEncryption) : ''

  if (storagePhoneDecryption && storagePhoneDecryption.split(',').length >= options.limit) {
    return storagePhoneDecryption
  } else {
    return ''
  }
}

export function setBlackPhone(phone) {
  const blackPhone = getBlackPhone({ limit: 1 })
  const phoneSet = new Set(blackPhone ? (blackPhone + ',' + phone).split(',') : [phone])
  const phoneString = Array.from(phoneSet).join(',')
  uni.setStorageSync('bp', encryptByDES(phoneString))
}

export function clearBlackPhone() {
  uni.removeStorageSync('bp')
}
