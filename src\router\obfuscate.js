// 自动生成混淆路由的函数
function generateObfuscatedPath(originalPath) {
  // 使用原始路径作为种子生成一致的混淆路径
  let hash = 0
  for (let i = 0; i < originalPath.length; i++) {
    const char = originalPath.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // 转换为32位整数
  }

  // 将hash转换为正数并生成字符串
  const positiveHash = Math.abs(hash)
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  let temp = positiveHash

  // 生成8位混淆字符串
  for (let i = 0; i < 8; i++) {
    result += chars[temp % chars.length]
    temp = Math.floor(temp / chars.length)
  }

  return '/' + result
}

// 创建路由混淆映射表
export function createRouteObfuscation(routeConfigs) {
  const routeMap = {}
  const reverseRouteMap = {}
  const usedObfuscatedPaths = new Set()
  const collisions = []

  routeConfigs.forEach((config) => {
    const obfuscatedPath = generateObfuscatedPath(config.originalPath)

    // 防重复检测
    if (usedObfuscatedPaths.has(obfuscatedPath)) {
      // 记录碰撞信息
      const existingOriginalPath = reverseRouteMap[obfuscatedPath]
      const collision = {
        obfuscatedPath,
        originalPath1: existingOriginalPath,
        originalPath2: config.originalPath,
        timestamp: new Date().toISOString()
      }
      collisions.push(collision)

      // 输出警告日志
      console.error('🚨 路由混淆碰撞检测到!')
      console.error('混淆路径:', obfuscatedPath)
      console.error('原始路径1:', existingOriginalPath)
      console.error('原始路径2:', config.originalPath)
      console.error('请检查路由配置或考虑增加混淆位数')

      // 抛出错误，阻止应用启动
      throw new Error(
        `路由混淆碰撞: "${existingOriginalPath}" 和 "${config.originalPath}" 都生成了相同的混淆路径 "${obfuscatedPath}"`
      )
    }

    // 记录已使用的混淆路径
    usedObfuscatedPaths.add(obfuscatedPath)
    routeMap[config.originalPath] = obfuscatedPath
    reverseRouteMap[obfuscatedPath] = config.originalPath
  })

  // 输出统计信息
  console.log('✅ 路由混淆完成')
  console.log('📊 统计信息:')
  console.log('  - 总路由数:', routeConfigs.length)
  console.log('  - 唯一混淆路径数:', usedObfuscatedPaths.size)
  console.log('  - 碰撞数量:', collisions.length)

  if (collisions.length === 0) {
    console.log('  - 状态: 🎉 无碰撞')
  }

  return {
    routeMap,
    reverseRouteMap,
    // 返回碰撞信息供调试使用
    collisions,
    stats: {
      totalRoutes: routeConfigs.length,
      uniqueObfuscatedPaths: usedObfuscatedPaths.size,
      collisionCount: collisions.length
    }
  }
}

// 路由辅助工具
export function createRouteUtils(routeMap, reverseRouteMap) {
  return {
    // 获取混淆后的路径
    getObfuscatedPath(originalPath) {
      return routeMap[originalPath] || originalPath
    },

    // 获取原始路径
    getOriginalPath(obfuscatedPath) {
      return reverseRouteMap[obfuscatedPath] || obfuscatedPath
    }
  }
}
