import axios from 'axios'
import { Toast } from 'vant'
import { encryptByDES, decryptByDES } from '@/utils/encrypt.js'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const { code, data, message } = response.data

    if (code === 200) {
      return data
    } else {
      Toast.fail(message || '请求失败')
      return Promise.reject(new Error(message || 'Error'))
    }
  },
  (error) => {
    const { response } = error

    if (response) {
      switch (response.status) {
        case 401:
          Toast.fail('登录已过期，请重新登录')
          localStorage.removeItem('token')
          // 跳转到登录页
          break
        case 403:
          Toast.fail('没有权限访问')
          break
        case 404:
          Toast.fail('请求的资源不存在')
          break
        case 500:
          Toast.fail('服务器内部错误')
          break
        default:
          Toast.fail('网络错误')
      }
    } else {
      Toast.fail('网络连接失败')
    }

    return Promise.reject(error)
  }
)

/**
 * GET请求
 * @param {Object} options 请求配置
 * @param {Object} options.data 请求参数
 * @param {String} options.url 接口地址
 * @param {Boolean} [options.isEncrypt=false] 参数是否需要加密
 * @param {Boolean} [options.deEncrypt=false] 响应数据是否需要解密
 * @param {Object} [options.header={}] 自定义请求头
 * @returns {Promise} 请求Promise对象
 */
export function $get({ data = {}, url, isEncrypt = false, deEncrypt = false, header = {} }) {
  // 处理请求数据加密
  let requestData = { ...data }

  if (isEncrypt) {
    requestData = {
      encrypt: encryptByDES(JSON.stringify(requestData))
    }
  }

  return service({
    method: 'GET',
    url,
    params: requestData, // GET请求使用params
    headers: header
  }).then(response => {
    // 处理响应数据解密
    if (deEncrypt && response.data) {
      try {
        response.data = decryptByDES(response.data)
      } catch (error) {
        console.error('解密响应数据失败:', error)
        throw new Error('数据解密失败')
      }
    }
    return response
  })
}

/**
 * POST请求
 * @param {Object} options 请求配置
 * @param {Object} options.data 请求参数
 * @param {String} options.url 接口地址
 * @param {Boolean} [options.isEncrypt=false] 参数是否需要加密
 * @param {Boolean} [options.deEncrypt=false] 响应数据是否需要解密
 * @param {Object} [options.header={}] 自定义请求头
 * @returns {Promise} 请求Promise对象
 */
export function $post({ data = {}, url, isEncrypt = false, deEncrypt = false, header = {} }) {
  // 处理请求数据加密
  let requestData = { ...data }

  if (isEncrypt) {
    requestData = {
      encrypt: encryptByDES(JSON.stringify(requestData))
    }
  }

  return service({
    method: 'POST',
    url,
    data: requestData, // POST请求使用data
    headers: header
  }).then(response => {
    // 处理响应数据解密
    if (deEncrypt && response.data) {
      try {
        response.data = decryptByDES(response.data)
      } catch (error) {
        console.error('解密响应数据失败:', error)
        throw new Error('数据解密失败')
      }
    }
    return response
  })
}

/**
 * 获取基础URL
 * @returns {String} 基础URL
 */
export function getBaseUrl() {
  return import.meta.env.VITE_APP_BASE_URL
}

export default service
