{"name": "xm-h5", "version": "1.0.0", "description": "基于 Vue3 + Vite + Vant 构建的移动端H5项目", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "check": "biome check ./src", "format": "biome format --write ./src", "fix": "biome check --write ./src"}, "dependencies": {"axios": "^1.11.0", "crypto-js": "^4.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vant": "^4.9.20", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "@vitejs/plugin-vue": "^6.0.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "sass": "^1.89.2", "terser": "^5.43.1", "vite": "^6.0.6", "vite-plugin-compression2": "^2.2.0"}, "keywords": ["vue3", "vite", "vant", "h5", "mobile"], "author": "", "license": "MIT"}