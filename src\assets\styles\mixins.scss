// 多行文本省略 mixin
@mixin multi-line-ellipsis($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 1px边框 mixin
@mixin hairline-border($color: #ebedf0, $direction: 'all') {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    
    @if $direction == 'top' {
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      border-top: 1px solid $color;
      transform-origin: 0 0;
    } @else if $direction == 'bottom' {
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      border-bottom: 1px solid $color;
      transform-origin: 0 100%;
    } @else {
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 1px solid $color;
      transform-origin: 0 0;
    }
    
    @media (-webkit-min-device-pixel-ratio: 2) {
      transform: scale(0.5);
      
      @if $direction == 'all' {
        width: 200%;
        height: 200%;
      }
    }
    
    @media (-webkit-min-device-pixel-ratio: 3) {
      transform: scale(0.33);
      
      @if $direction == 'all' {
        width: 300%;
        height: 300%;
      }
    }
  }
}